import { Router } from 'express';
import GarmentController from '../controllers/GarmentController';

const garmentsRoutes = Router();

garmentsRoutes.get('/', GarmentController.getAll);
garmentsRoutes.get('/:id', GarmentController.getById);
garmentsRoutes.post('/', GarmentController.create);
garmentsRoutes.patch('/:id', GarmentController.patch);
garmentsRoutes.delete('/:id', GarmentController.delete);

export default garmentsRoutes;