import { Router } from 'express';
import CustomerController from '../controllers/CustomerController';

const customersRoutes = Router();

customersRoutes.get('/', CustomerController.getAll);
customersRoutes.get('/:id', CustomerController.getById);
customersRoutes.post('/', CustomerController.create);
customersRoutes.patch('/:id', CustomerController.patch);
customersRoutes.delete('/:id', CustomerController.delete);

export default customersRoutes;