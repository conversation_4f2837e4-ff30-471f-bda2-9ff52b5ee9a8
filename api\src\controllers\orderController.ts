import type { Request, Response } from 'express';
import { User } from '../models/user/userModel';
import type { IUserWithStaffData } from '../models/user/iUserWithStaffData';
import { Customer } from '../models/customer/customerModel';
import { Order } from '../models/order/orderModel';
import type { IGarmentIntoOrder } from '../models/order/IGarmentIntoOrder';
import { customerRepository } from '../repositories/customerRepository';
import { orderRepository } from '../repositories/orderRepository';
import { userRepository } from '../repositories/userRepository';
import { garmentRepository } from '../repositories/garmentRepository';
import { ICreateOrderDto } from '../dto/OrderDto';

class OrderController {
    public static async create(req: Request, res: Response) {
        try {
            const { customer_id, staff_id, deadline, status, garments }: { customer_id: number, staff_id: number, deadline: string, status: string, garments: IGarmentIntoOrder[] } = req.body;
    
            const customer = await customerRepository.findOneBy({ id: customer_id });
            if (!customer) {
                return res.status(404).json({ message: 'Cliente não encontrado!' });
            }
    
            const staff = await userRepository.findOneBy({ id: staff_id });
            if (!staff) {
                return res.status(404).json({ message: 'Funcionário não encontrado!' });
            }
    
            if (!deadline) {
                return res.status(400).json({ message: 'Prazo não foi definido!' });
            }

            if (!status) { 
                return res.status(400).json({ message: 'Status não foi definido!' });
            }
    
            if (!Array.isArray(garments) || garments.length === 0) {
                return res.status(400).json({ error: 'É necessário incluir ao menos 1 modelo no pedido!' });
            }

            const date: Date = new Date();
            const time: string = new Date().toLocaleTimeString()

            const order: ICreateOrderDto = {
                customer,
                user: staff,
                date,
                time,
                deadline,
                status,
                garments
            };
    
            const newOrder = orderRepository.create(order);
            await orderRepository.saveOrderCompletely(newOrder);
    
            return res.status(201).json({ message: 'Pedido criado com sucesso!' });
        } catch (error) {
            console.error(error);
            return res.status(500).json({ message: 'Internal server error' });
        }
    }

    public static async getAll(req: Request, res: Response) {
        try {
            const orders = await orderRepository.find({ relations: { garments: true, user: true, customer: true }});

            if (!orders || orders.length === 0) {
                return res.status(404).json({ message: 'Nenhum pedido encontrado!' });
            }

            return res.status(200).json(orders);
        } catch (error) {
            console.error('Error fetching orders:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }

    public static async getById(req: Request, res: Response) {
        const { id } = req.params;
        try {
            const order = await orderRepository.findOne({ where: { id: Number(id) }, relations: { garments: true, user: true, customer: true }});
            if (!order) {
                return res.status(404).json({ message: 'Pedido não encontrado!' });
            }
            return res.status(200).json(order);
        } catch (error) {
            console.error('Error fetching order by ID:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }

    public static async patch(req: Request, res: Response) {
        const { id } = req.params;
        const updates = req.body;
    
        try {
            const existingOrder = await Order.getOrderById(Number(id));
            if (!existingOrder) {
                return res.status(404).json({ error: 'Pedido não encontrado!' });
            }
    
            const updated = await Order.patchOrder(Number(id), updates);
    
            if (!updated) {
                return res.status(500).json({ error: 'Falha ao atualizar pedido!' });
            }
    
            return res.status(200).json({ message: 'Pedido atualizado com sucesso!'});
        } catch (error) {
            console.error('Error updating order:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }

    public static async delete(req: Request, res: Response) {
        const { id } = req.params;
    
        try {
            const result = await Order.deleteOrder(Number(id));
    
            if (result) {
                return res.status(200).json({ message: 'Pedido deletado com sucesso!' });
            }
                return res.status(404).json({ error: 'Pedido não encontrado!' });
        } catch (error) {
            console.error('Error deleting order:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }
}

export default OrderController;