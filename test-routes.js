const http = require('http');

function testRoute(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    console.log(`Testing ${method} ${path}...`);

    const options = {
      hostname: 'localhost',
      port: 3333,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        try {
          const jsonBody = JSON.parse(body);
          console.log('Response:', JSON.stringify(jsonBody, null, 2));
        } catch (e) {
          console.log('Response:', body);
        }
        console.log('---');
        resolve();
      });
    });

    req.on('error', (err) => {
      console.log(`Error: ${err.message}`);
      console.log('---');
      resolve();
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAllGarmentRoutes() {
  console.log('=== TESTING GARMENT API ROUTES ===\n');
  
  // Test basic API route
  console.log('1. Testing basic API route...');
  await testRoute('/');
  
  // Test GET all garments
  console.log('2. Testing GET all garments...');
  await testRoute('/garments');
  
  // Test GET garment by ID (testing with ID 1)
  console.log('3. Testing GET garment by ID...');
  await testRoute('/garments/1');
  
  // Test POST create garment
  console.log('4. Testing POST create garment...');
  const newGarment = {
    name: 'Camiseta Teste',
    refcode: 'TEST001',
    price: 29.99,
    size: 'M',
    color: 'Azul',
    in_stock: 10
  };
  await testRoute('/garments', 'POST', newGarment);
  
  // Test GET all garments again to see if the new garment was created
  console.log('5. Testing GET all garments after creation...');
  await testRoute('/garments');
  
  // Test PATCH update garment (assuming the created garment has ID 1 or higher)
  console.log('6. Testing PATCH update garment...');
  const updateData = {
    price: 35.99,
    in_stock: 15
  };
  await testRoute('/garments/1', 'PATCH', updateData);
  
  // Test GET garment by ID after update
  console.log('7. Testing GET garment by ID after update...');
  await testRoute('/garments/1');
  
  // Test DELETE garment (soft delete)
  console.log('8. Testing DELETE garment...');
  await testRoute('/garments/1', 'DELETE');
  
  // Test GET garment by ID after delete
  console.log('9. Testing GET garment by ID after delete...');
  await testRoute('/garments/1');
  
  console.log('\n=== ALL GARMENT ROUTE TESTS COMPLETED ===');
}

testAllGarmentRoutes().catch(console.error);
