import { Router } from 'express';
import UserController from '../controllers/UserController';

const usersRoutes = Router();

usersRoutes.get('/', UserController.getAll);
usersRoutes.get('/:id', UserController.getById)
usersRoutes.post('/', UserController.create);
usersRoutes.post('/login', UserController.login);
usersRoutes.patch('/:id', UserController.patch);
usersRoutes.delete('/:id', UserController.delete);

export default usersRoutes;