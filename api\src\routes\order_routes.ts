import { Router } from 'express';
import OrderController from '../controllers/OrderController';

const ordersRoutes = Router();

ordersRoutes.get('/', OrderController.getAll);
ordersRoutes.get('/:id', OrderController.getById);
ordersRoutes.post('/', OrderController.create);
ordersRoutes.patch('/:id', OrderController.patch);
ordersRoutes.delete('/:id', OrderController.delete);

export default ordersRoutes;